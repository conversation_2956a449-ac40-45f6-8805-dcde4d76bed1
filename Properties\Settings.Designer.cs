﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ShipmentRecord.Properties
{
    /// <summary>
    /// 应用程序设置类
    /// 用于管理应用程序的配置设置
    /// </summary>
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute(@"Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", @"********")]
    internal sealed partial class Settings : global::System.Configuration.ApplicationSettingsBase
    {
        /// <summary>
        /// 默认设置实例
        /// </summary>
        private static Settings defaultInstance = ((Settings)(global::System.Configuration.ApplicationSettingsBase.Synchronized(new Settings())));

        /// <summary>
        /// 获取默认设置实例
        /// </summary>
        public static Settings Default
        {
            get { return defaultInstance; }
        }
    }
}