namespace ShipmentRecord
{
    /// <summary>
    /// 客户信息类
    /// 用于存储和管理客户的基本信息
    /// </summary>
    public class Customer
    {
        /// <summary>
        /// 客户名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 客户地址
        /// </summary>
        public string Address { get; set; }
        
        /// <summary>
        /// 客户电话号码
        /// </summary>
        public string PhoneNumber { get; set; }
        
        /// <summary>
        /// 客户电子邮箱
        /// </summary>
        public string Email { get; set; }
    }
}