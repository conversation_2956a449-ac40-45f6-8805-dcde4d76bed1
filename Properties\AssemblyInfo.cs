﻿using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

// 程序集的一般信息由以下特性集控制
// 更改这些特性值可修改与程序集关联的信息
// General Information about an assembly is controlled through the following
// set of attributes. Change these attribute values to modify the information
// associated with an assembly.
[assembly: AssemblyTitle(@"ShipmentRecord")]
[assembly: AssemblyDescription(@"")]
[assembly: AssemblyConfiguration(@"")]
[assembly: AssemblyCompany(@"")]
[assembly: AssemblyProduct(@"ShipmentRecord")]
[assembly: AssemblyCopyright(@"Copyright ©  2025")]
[assembly: AssemblyTrademark(@"")]
[assembly: AssemblyCulture(@"")]

// 将 ComVisible 设置为 false 会使此程序集中的类型对 COM 组件不可见
// 如果需要从 COM 访问此程序集中的类型，请将该类型的 ComVisible 特性设置为 true
// Setting ComVisible to false makes the types in this assembly not visible
// to COM components.  If you need to access a type in this assembly from
// COM, set the ComVisible attribute to true on that type.
[assembly: ComVisible(false)]

// 如果此项目向 COM 公开，则下列 GUID 用于类型库的 ID
// The following GUID is for the ID of the typelib if this project is exposed to COM
[assembly: Guid(@"E10DDD03-0A3D-4106-994E-FA6D526F2AEE")]

// 程序集的版本信息由下列四个值组成:
// 主版本号、次版本号、生成号、修订号
// Version information for an assembly consists of the following four values:
//
//      Major Version
//      Minor Version
//      Build Number
//      Revision
//
// 可以指定所有值，也可以使用"*"默认修订号和生成号
// You can specify all the values or you can default the Build and Revision Numbers
// by using the '*' as shown below:
// [assembly: AssemblyVersion("1.0.*")]
[assembly: AssemblyVersion(@"3.0.0.0")]
[assembly: AssemblyFileVersion(@"3.0.0.0")]