using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;
using AiHelper;
using BsjHelperV2.Helper;
using Kyozy.MiniblinkNet;
using MiniExcelLibs;
using Newtonsoft.Json;
using SerialPortHelperLib;
using Parity = SerialPortHelperLib.Parity;
using SerialData = SerialPortHelperLib.SerialData;
using StopBits = SerialPortHelperLib.StopBits;

namespace ShipmentRecord
{
    /// <summary>
    /// 主窗体类
    /// 实现出货记录管理的主要功能界面
    /// </summary>
    public partial class FormMain : Form
    {
        #region 全局属性变量

        /// <summary>
        /// 定义浏览器控件
        /// 使用MiniBlink浏览器内核,用于访问内部网站获取产品信息
        /// </summary>
        private WebView MiniBlinkBrowser { get; } = new WebView { NavigationToNewWindowEnable = false };

        /// <summary>
        /// 应用程序配置信息
        /// 包含客户列表、型号列表等系统配置数据
        /// </summary>
        private SettingConfig Setting { get; set; }

        /// <summary>
        /// 当前选择的客户名称
        /// </summary>
        private string CustomerName { get; set; } = string.Empty;

        /// <summary>
        /// 当前选择的产品型号名称
        /// </summary>
        private string ModelName { get; set; } = string.Empty;

        /// <summary>
        /// 当前扫描的箱号
        /// </summary>
        private string BoxSn { get; set; } = string.Empty;

        /// <summary>
        /// 产品列表 - 存储当前会话中所有扫描的产品信息
        /// </summary>
        private List<ShipmentRecordProduct> ProductList { get; set; } = new List<ShipmentRecordProduct>();

        /// <summary>
        /// 查询结果产品列表
        /// 存储通过SN或箱号查询到的产品记录，用于在追溯页面显示
        /// </summary>
        private List<ShipmentRecordProduct> ObjProductList { get; set; } = new List<ShipmentRecordProduct>();

        /// <summary>
        /// 扫描枪串口助手对象 - 用于管理与扫描枪的串口通信
        /// </summary>
        private SerialPortHelper SerialPortHelperScan { get; set; }

        /// <summary>
        /// 重载WndProc方法,用于监听串口设备的插拔事件
        /// 当有新设备连接或设备移除时,自动更新串口列表
        /// </summary>
        protected override void WndProc(ref Message m)
        {
            base.WndProc(ref m); //调用父类方法，以确保其他功能正常
            switch (m.Msg)
            {
                case 0x219: //设备改变事件
                    switch ((int)m.WParam)
                    {
                        case 0x8000: //检测到新设备
                            UpdateSerialPort();
                            break;

                        case 0x8004: //有设备移除
                            UpdateSerialPort();
                            break;
                    }

                    break;
            }
        }

        #endregion

        /// <summary>
        /// 构造函数
        /// 初始化窗体组件
        /// </summary>
        public FormMain()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 窗体加载事件
        /// 初始化界面控件、加载配置信息、初始化串口和浏览器
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void FormMain_Load(object sender, EventArgs e)
        {
            try
            {
                Text += $@" V:{Application.ProductVersion}";
                SoundHelper.InitSoundService();

                // 初始化数据网格控件
                ControlHelper.MyControl.InitializeDataGridView(dataGridView1);
                ControlHelper.MyControl.InitializeDataGridView(dataGridView2);

                // 读取客户配置文件
                string configText = File.ReadAllText(@"Setting.json");
                Setting = JsonConvert.DeserializeObject<SettingConfig>(configText);
                ShowInfo();

                // 更新串口列表
                UpdateSerialPort();

                // 初始化浏览器
                InitializeMiniBlinkBrowser();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 更新串口列表
        /// 获取系统当前可用的串口,并填充到下拉列表中
        /// </summary>
        private void UpdateSerialPort()
        {
            // 清空当前串口列表
            comboBox_SerialPortScan.Items.Clear();

            // 获取系统串口并排序
            string[] ports = SerialPort.GetPortNames().OrderBy(x => x).ToArray();
            foreach (string port in ports)
            {
                comboBox_SerialPortScan.Items.Add(port);
            }

            // 默认选择第一个串口
            comboBox_SerialPortScan.SelectedIndex = 0;
            // comboBox_SerialPortScan.Text = comboBox_SerialPortScan.Items.Contains(Setting.Current.SerialPortScan) ? Setting.Current.SerialPortScan : ports.FirstOrDefault();
        }

        #region MiniBlink事件

        /// <summary>
        /// 初始化浏览器
        /// 配置MiniBlink浏览器控件并绑定相关事件
        /// </summary>
        private void InitializeMiniBlinkBrowser()
        {
            // 删除旧的浏览器缓存文件
            DeleteMiniBlinkFile();
            if (MiniBlinkBrowser.Bind(panel_Miniblink))
            {
                // 绑定浏览器事件
                // MiniBlinkBrowser.OnURLChange += MiniBlinkBrowserOnUrlChange;
                MiniBlinkBrowser.OnLoadUrlBegin += MiniBlinkBrowserOnUrlBegin;
                MiniBlinkBrowser.OnLoadUrlEnd += MiniBlinkBrowserOnUrlEnd;
                // MiniBlinkBrowser.OnDocumentReady += MiniBlinkBrowserOnDocumentReady;
                MiniBlinkBrowser.OnAlertBox += MiniBlinkBrowserOnAlertBox;
                // 加载内部网站
                MiniBlinkBrowser.LoadURL(@"http://*************:8090/");
            }
        }

        /// <summary>
        /// 删除旧的浏览器数据
        /// 清理浏览器缓存和Cookie文件
        /// </summary>
        private void DeleteMiniBlinkFile()
        {
            // 删除本地存储目录
            if (Directory.Exists(Application.StartupPath + @"/LocalStorage"))
            {
                Directory.Delete(Application.StartupPath + @"/LocalStorage", true);
            }

            // 删除Cookie文件
            if (File.Exists(Application.StartupPath + @"/cookies.dat"))
            {
                File.Delete(Application.StartupPath + @"/cookies.dat");
            }
        }

        /// <summary>
        /// 浏览地址发生变化事件
        /// 当浏览器URL发生变化时触发
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">URL变化事件参数</param>
        private void MiniBlinkBrowserOnUrlChange(object sender, UrlChangeEventArgs e)
        {
            Debug.WriteLine(e.URL);
        }

        /// <summary>
        /// 开始载入新网址事件
        /// 当浏览器开始加载新URL时触发
        /// 用于设置网络请求的钩子,以便后续处理响应数据
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">加载URL开始事件参数</param>
        private void MiniBlinkBrowserOnUrlBegin(object sender, LoadUrlBeginEventArgs e)
        {
            Debug.WriteLine($@"begin load url:{e.URL}");
            if (e.URL.Contains(@"http://*************:8090/Content/console/2.0/report.jpg"))
            {
                tabControl1.SelectedIndex = 1;
                panel_Miniblink.Visible = false;
                label_LoginOK.Visible = true;
                label_LoginOK2.Visible = true;
                SoundHelper.登录成功.Play();
            }

            if (e.URL.Equals(@"http://*************:8090/Manufacture/BasicInfo.aspx?name=Manufacture_BasicInfo"))
            {
                //开启缓存网络请求返回的数据
                WebView.NetHookRequest(e.Job);
            }
        }

        /// <summary>
        /// 载入新网址完毕事件
        /// 当浏览器完成URL加载时触发
        /// 用于处理网络响应数据,提取产品信息并保存到数据库
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">加载URL结束事件参数</param>
        private void MiniBlinkBrowserOnUrlEnd(object sender, LoadUrlEndEventArgs e)
        {
            try
            {
                if (e.URL.Equals(@"http://*************:8090/Manufacture/BasicInfo.aspx?name=Manufacture_BasicInfo"))
                {
                    // 取出缓存的网络数据
                    byte[] data = e.Data;
                    string dataStr = Encoding.UTF8.GetString(data);
                    // Debug.WriteLine(dataStr);

                    // 通过正则表达式提取产品序列号信息
                    List<ShipmentRecordProduct> products = new List<ShipmentRecordProduct>();
                    Regex regex = new Regex(@"<span title=.*>.*</span>");
                    MatchCollection matches = regex.Matches(dataStr);
                    foreach (Match match in matches)
                    {
                        string value = Ai.GetTextMiddle(match.Value, @">", @"</span>");
                        // Debug.WriteLine(value);

                        // 创建产品记录对象
                        ShipmentRecordProduct shipmentRecordProduct = new ShipmentRecordProduct
                        {
                            Sn = value,
                            BoxSn = BoxSn,
                            CustomerName = CustomerName,
                            Model = ModelName
                        };
                        products.Add(shipmentRecordProduct);
                    }

                    if (products.Any())
                    {
                        // 添加产品到内存列表
                        ProductList.AddRange(products);
                        // 保存产品信息到数据库
                        DbHelper.FSql.Insert(products).ExecuteAffrowsAsync();
                        // 更新已录入数量标签
                        Invoke(new Action(() => { label5.Text = $@"已录入数量:{ProductList.Count}"; }));
                        // 更新数据网格视图
                        Invoke(new Action(() =>
                        {
                            dataGridView1.DataSource = null;
                            dataGridView1.DataSource = ProductList;
                            dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
                            dataGridView1.Refresh();
                            // 滚动到最新记录
                            dataGridView1.FirstDisplayedScrollingRowIndex = dataGridView1.Rows.Count - 1;
                        }));
                        SoundHelper.录入成功.Play();
                    }
                    else
                    {
                        SoundHelper.录入失败.Play();
                        MessageBox.Show(@"该箱号未找到货品信息!");
                    }
                }
                // else
                // {
                //     if (!string.IsNullOrWhiteSpace(BoxSn))
                //     {
                //         MessageBox.Show(@"查询出错,请检查网络连接或联系管理员!");
                //     }
                // }
            }
            catch (Exception ex)
            {
                MessageBox.Show($@"操作出错,{ex.Message}!");
            }
            finally
            {
                // 更新箱号标签并清空当前箱号
                Invoke(new Action(() => { label_BoxSn.Text = @"箱号:" + BoxSn + @" 查询完成."; }));
                BoxSn = @"";
            }
        }

        /// <summary>
        /// 文档载入完毕事件
        /// 当浏览器完成文档加载时触发
        /// 用于查看和处理Cookie信息
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">文档就绪事件参数</param>
        private void MiniBlinkBrowserOnDocumentReady(object sender, DocumentReadyEventArgs e)
        {
            try
            {
                // 定义访问Cookie的回调函数
                bool Visitor(IntPtr userData, string name, string value, string domain, string path, int secure, int httpOnly, ref int expires)
                {
                    Debug.WriteLine(@"name={0},value={1},domain={2},path={3},secure={4},httpOnly={5},expires={6}", name, value, domain, path, secure, httpOnly, expires);
                    return false;
                }

                // 访问所有Cookie
                MiniBlinkBrowser.VisitAllCookie(Visitor, IntPtr.Zero);
            }
            catch (Exception ex)
            {
                Debug.WriteLine(ex);
            }
        }

        /// <summary>
        /// 重写Alert事件
        /// 屏蔽浏览器的弹窗提示
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">弹窗事件参数</param>
        private void MiniBlinkBrowserOnAlertBox(object sender, AlertBoxEventArgs e)
        {
            // 留空,不显示任何alert信息
        }

        /// <summary>
        /// 执行JavaScript代码
        /// 在浏览器中执行指定的JS代码并获取返回值
        /// </summary>
        /// <param name="jsStr">要执行的JavaScript代码</param>
        private void BrowserRunJs(string jsStr)
        {
            var v = MiniBlinkBrowser.RunJS(jsStr);
            Debug.WriteLine(v.ToString(MiniBlinkBrowser.GlobalExec()));
        }

        /// <summary>
        /// 在特定框架中执行JavaScript代码
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">文档就绪事件参数</param>
        private void BrowserRunJsByFrame(object sender, DocumentReadyEventArgs e)
        {
            JsValue v = MiniBlinkBrowser.RunJsByFrame(e.Frame, @"document.getElementsByTagName('html')[0].outerHTML;");
            //System.Diagnostics.Debug.WriteLine(v.ToString(m_wke.GlobalExec()));
        }

        #endregion

        /// <summary>
        /// 客户选择下拉列表选择变化事件
        /// 当用户选择不同客户时更新显示标签和当前客户名称
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void comboBox_Customer_SelectedIndexChanged(object sender, EventArgs e)
        {
            label3.Text = comboBox_Customer.Text + @"@" + comboBox_Model.Text;
            CustomerName = comboBox_Customer.Text;
        }

        /// <summary>
        /// 产品型号下拉列表选择变化事件
        /// 当用户选择不同型号时更新显示标签和当前型号名称
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void comboBox_Model_SelectedIndexChanged(object sender, EventArgs e)
        {
            label3.Text = comboBox_Customer.Text + @"@" + comboBox_Model.Text;
            ModelName = comboBox_Model.Text;
        }

        /// <summary>
        /// 串口开关按钮点击事件
        /// 打开或关闭扫描枪串口连接
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void button_SerialPortScan_Click(object sender, EventArgs e)
        {
            if (comboBox_Customer.SelectedIndex.Equals(0) || comboBox_Model.SelectedIndex.Equals(0))
            {
                MessageBox.Show(@"请先选择出货客户和出货产品型号!");
                return;
            }

            if (button_SerialPortScan.Text == @"打开")
            {
                SerialPortHelperScan = new SerialPortHelper(new ConfigComType
                {
                    PortName = comboBox_SerialPortScan.Text,
                    BaudRate = 115200,
                    DataBits = 8,
                    StopBits = StopBits.One,
                    Parity = Parity.None
                });
                SerialPortHelperScan.BindSerialPortDataReceivedProcessEvent(SerialPortDataReceivedProcessScan);
                SerialPortHelperScan.BindSerialPortErrorEvent(SerialPortErrorProcessScan);
                SerialPortHelperScan.OpenCom(out string strError);
                if (strError != @"null")
                {
                    //串口开启失败
                    MessageBox.Show(strError);
                    return;
                }

                // ShowLog($"{Ai.中括号左}{SerialPortHelperScan.ConfigSerialPort.PortName}{Ai.中括号右} 打开成功.波特率:{Ai.中括号左}{Setting.Current.SerialPortScanBaudRate}{Ai.中括号右}.", LogType.Info);
                button_SerialPortScan.Text = @"关闭";
                button_SerialPortScan.ForeColor = System.Drawing.Color.Green;
            }
            else
            {
                SerialPortHelperScan.CloseCom(out string strError);
                if (strError != @"null")
                {
                    //关闭串口失败
                    MessageBox.Show(strError);
                    return;
                }

                // ShowLog($"{Ai.中括号左}{SerialPortHelperScan.ConfigSerialPort.PortName}{Ai.中括号右} 关闭成功.", LogType.Info);
                button_SerialPortScan.Text = @"打开";
                button_SerialPortScan.ForeColor = System.Drawing.Color.Black;
            }
        }

        /// <summary>
        /// 串口接收数据处理事件
        /// 处理从扫描枪串口接收到的箱号数据，验证并触发查询操作
        /// </summary>
        /// <param name="sender">串口助手类对象</param>
        /// <param name="arrData">接收到的字节数据数组</param>
        private void SerialPortDataReceivedProcessScan(object sender, byte[] arrData)
        {
            SerialPortHelper spb = (SerialPortHelper)sender;
            Invoke(new Action(() =>
            {
                try
                {
                    string strData = SerialData.ToString(arrData);
                    // string hexData = SerialData.ToHexString(arrData);
                    string recData = strData.Trim();
                    if (!string.IsNullOrWhiteSpace(BoxSn))
                    {
                        MessageBox.Show(@"请等待上一个查询完成!");
                        return;
                    }

                    BoxSn = recData;
                    label_BoxSn.Text = @"箱号:" + BoxSn + @" 查询中...";

                    // 从数据库中查询是否有该SN
                    ShipmentRecordProduct obj = DbHelper.FSql.Select<ShipmentRecordProduct>().Where(a => a.BoxSn == recData).First();
                    if (obj != null)
                    {
                        label_BoxSn.Text = @"箱号:" + BoxSn + @" 已存在!";
                        BoxSn = @"";
                        SoundHelper.录入失败.Play();
                        MessageBox.Show($@"该箱号已于{obj.AddTime:yyyy-MM-dd HH:mm:ss} 录入,请勿重复录入!");
                        return;
                    }

                    Task.Run(DoWork);
                }
                catch (Exception ex)
                {
                    Console.WriteLine(spb.ConfigSerialPort.PortName + @":接收数据异常，" + ex.Message);
                }
            }));
        }

        /// <summary>
        /// 串口错误处理事件
        /// 处理串口通信过程中发生的各种错误情况
        /// </summary>
        /// <param name="sender">串口助手类对象</param>
        /// <param name="enumError">错误类型枚举</param>
        /// <param name="strError">错误详细信息</param>
        private void SerialPortErrorProcessScan(object sender, enumSerialError enumError, string strError)
        {
            Invoke(new Action(() =>
            {
                SerialPortHelper spb = (SerialPortHelper)sender;
                switch (enumError)
                {
                    case enumSerialError.LinkError:
                        spb.CloseCom(out string str);
                        Console.WriteLine(spb.SerialMark + @"串口错误：" + strError);
                        Console.WriteLine(str);
                        //MessageBox.Show(strError, @"串口错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        break;
                    case enumSerialError.WriteError:
                        Console.WriteLine(spb.SerialMark + @"发送错误：" + strError);
                        break;
                    case enumSerialError.ReceivedError:
                        Console.WriteLine(spb.SerialMark + @"接收错误：" + strError);
                        break;
                }
            }));
        }

        /// <summary>
        /// 执行货品信息查询工作
        /// 通过浏览器向MES系统发送POST请求查询指定箱号的产品信息
        /// </summary>
        private void DoWork()
        {
            Invoke(new Action(() =>
            {
                try
                {
                    string postData =
                        $@"treeBoardCount_ExpandState=&treeBoardCount_SelectedNode=&__EVENTTARGET=&__EVENTARGUMENT=&treeBoardCount_PopulateLog=&treeAssemble_ExpandState=&treeAssemble_SelectedNode=&treeAssemble_PopulateLog=&tvBoxPack_ExpandState=ennnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnn&tvBoxPack_SelectedNode=&tvBoxPack_PopulateLog=&tvmodelgroup_ExpandState=&tvmodelgroup_SelectedNode=&tvmodelgroup_PopulateLog=&__VIEWSTATE=%2FwEPDwULLTE1MDA4MTU2MDZkGAEFHl9fQ29udHJvbHNSZXF1aXJlUG9zdEJhY2tLZXlfXxYIBTtjdGwwMCRjdGwwMCRDb250ZW50UGxhY2VIb2xkZXIxJHZpZXdjb250ZW50JHJkb1NlcmlhbE51bWJlcgU7Y3RsMDAkY3RsMDAkQ29udGVudFBsYWNlSG9sZGVyMSR2aWV3Y29udGVudCRyZG9Qcm9kdWN0T3JkZXIFOGN0bDAwJGN0bDAwJENvbnRlbnRQbGFjZUhvbGRlcjEkdmlld2NvbnRlbnQkcmRvQm94TnVtYmVyBTZjdGwwMCRjdGwwMCRDb250ZW50UGxhY2VIb2xkZXIxJHZpZXdjb250ZW50JHJkb01vZGVsTm8FOmN0bDAwJGN0bDAwJENvbnRlbnRQbGFjZUhvbGRlcjEkdmlld2NvbnRlbnQkdHJlZUJvYXJkQ291bnQFOGN0bDAwJGN0bDAwJENvbnRlbnRQbGFjZUhvbGRlcjEkdmlld2NvbnRlbnQkdHJlZUFzc2VtYmxlBTVjdGwwMCRjdGwwMCRDb250ZW50UGxhY2VIb2xkZXIxJHZpZXdjb250ZW50JHR2Qm94UGFjawU4Y3RsMDAkY3RsMDAkQ29udGVudFBsYWNlSG9sZGVyMSR2aWV3Y29udGVudCR0dm1vZGVsZ3JvdXC9SYt3uSpZ4bqZ79fQelMXzfmCBJhjcyD%2B7cOK%2FYla2A%3D%3D&ctl00%24ctl00%24ContentPlaceHolder1%24viewcontent%24txtSerialNumber=&ctl00%24ctl00%24ContentPlaceHolder1%24viewcontent%24txtProductOrder=&ctl00%24ctl00%24ContentPlaceHolder1%24viewcontent%24rdoSearchGroup=rdoBoxNumber&ctl00%24ctl00%24ContentPlaceHolder1%24viewcontent%24txtBoxNumber={BoxSn}&ctl00%24ctl00%24ContentPlaceHolder1%24viewcontent%24txtModelNo=&ctl00%24ctl00%24ContentPlaceHolder1%24viewcontent%24txtVersions=";

                    // 注入 JavaScript 代码来执行 POST 请求
                    string script = $@"
                    fetch('http://*************:8090/Manufacture/BasicInfo.aspx?name=Manufacture_BasicInfo', {{
                        method: 'POST',
                        headers: {{
                            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
                        }},
                        body: '{postData}'
                    }})
                    .then(response => {{ console.log(response.text()); }})
                    .catch(error => console.log('Error:', error));";
                    // await webView2_GameWeb.CoreWebView2.ExecuteScriptAsync(script);
                    BrowserRunJs(script);
                }
                catch (Exception ex)
                {
                    Debug.WriteLine(ex.Message);
                }
            }));
        }

        /// <summary>
        /// 查询按钮点击事件
        /// 触发数据检查操作
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void button_Check_Click(object sender, EventArgs e)
        {
            try
            {
                await Task.Run(CheckData);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 检查数据
        /// 根据输入的SN或箱号查询产品记录并在数据表格中显示结果
        /// </summary>
        private async void CheckData()
        {
            try
            {
                string sn = textBox_Sn.Text.Trim();
                if (string.IsNullOrWhiteSpace(sn))
                {
                    MessageBox.Show(@"请输入产品SN查询!");
                    return;
                }

                // 从数据库中查询是否有该SN
                ObjProductList = await DbHelper.FSql.Select<ShipmentRecordProduct>()
                    .Where(a => a.Sn == sn || a.BoxSn == sn)
                    .Where(a => a.IsDelete == false)
                    .ToListAsync();

                if (!ObjProductList.Any())
                {
                    SoundHelper.查询失败.Play();
                    MessageBox.Show($@"该SN:{sn} 未找到录入记录!");
                    return;
                }

                Invoke(new Action(() =>
                {
                    dataGridView2.Visible = false;
                    dataGridView2.DataSource = null;
                    dataGridView2.DataSource = ObjProductList;
                    dataGridView2.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
                    dataGridView2.FirstDisplayedScrollingRowIndex = dataGridView2.Rows.Count - 1;
                    dataGridView2.Refresh();
                    dataGridView2.Visible = true;
                }));
                SoundHelper.查询成功.Play();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 删除按钮点击事件
        /// 触发删除数据操作，根据输入的箱号删除对应的产品记录
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void button_Delete_Click(object sender, EventArgs e)
        {
            DeleteData();
        }

        /// <summary>
        /// 删除数据
        /// 根据输入的箱号删除对应的产品记录
        /// </summary>
        private void DeleteData()
        {
            try
            {
                button_Delete.Enabled = false;
                string boxSn = textBox_DeleteBoxSn.Text.Trim();
                if (string.IsNullOrWhiteSpace(boxSn))
                {
                    MessageBox.Show(@"请输入箱号!");
                    return;
                }

                // 从数据库中查询是否有该SN
                ShipmentRecordProduct obj = DbHelper.FSql.Select<ShipmentRecordProduct>().Where(a => a.BoxSn == boxSn).First();
                if (obj == null)
                {
                    SoundHelper.删除失败.Play();
                    MessageBox.Show($@"该箱号:{boxSn} 未找到录入记录!");
                    return;
                }

                int affRows = DbHelper.FSql.Update<ShipmentRecordProduct>()
                    .Set(a => a.IsDelete, true)
                    .Where(a => a.BoxSn == boxSn)
                    .ExecuteAffrows();

                SoundHelper.删除成功.Play();
                MessageBox.Show($@"删除成功!本次删除{affRows}条记录!");
                textBox_DeleteBoxSn.Text = @"";
            }
            catch (Exception ex)
            {
                SoundHelper.删除失败.Play();
                MessageBox.Show(ex.Message);
            }
            finally
            {
                button_Delete.Enabled = true;
            }
        }

        /// <summary>
        /// 显示界面信息
        /// 初始化并填充所有界面控件的数据，包括下拉列表和列表视图
        /// </summary>
        private void ShowInfo()
        {
            ShowCustomerListComboBox();
            ShowModelListComboBox();
            ShowCustomerListView();
            ShowModelListView();
        }

        /// <summary>
        /// 填充客户下拉列表
        /// 从配置文件中读取客户信息并填充到客户选择下拉列表中
        /// </summary>
        private void ShowCustomerListComboBox()
        {
            // 填充客户下拉列表
            comboBox_Customer.Items.Clear();
            comboBox_Customer.Items.Add(@"请选择客户");
            foreach (Customer item in Setting.CustomerList)
            {
                comboBox_Customer.Items.Add(item.Name);
            }

            comboBox_Customer.SelectedIndex = 0;
        }

        /// <summary>
        /// 填充型号下拉列表
        /// 从配置文件中读取产品型号信息并填充到型号选择下拉列表中
        /// </summary>
        private void ShowModelListComboBox()
        {
            // 填充型号下拉列表
            comboBox_Model.Items.Clear();
            comboBox_Model.Items.Add(@"型号");
            foreach (Model model in Setting.ModelList)
            {
                comboBox_Model.Items.Add(model.Name);
            }

            comboBox_Model.SelectedIndex = 0;
        }

        /// <summary>
        /// 填充客户列表视图
        /// 在设置页面显示所有客户信息，用于客户管理
        /// </summary>
        private void ShowCustomerListView()
        {
            listView_Customer.Items.Clear();
            foreach (Customer customer in Setting.CustomerList)
            {
                listView_Customer.Items.Add(new ListViewItem(new[] { customer.Name }));
            }
        }

        /// <summary>
        /// 填充型号列表视图
        /// 在设置页面显示所有产品型号信息，用于型号管理
        /// </summary>
        private void ShowModelListView()
        {
            listView_Model.Items.Clear();
            foreach (Model model in Setting.ModelList)
            {
                listView_Model.Items.Add(new ListViewItem(new[] { model.Name }));
            }
        }

        /// <summary>
        /// 添加客户按钮点击事件
        /// 将新客户信息添加到配置文件并更新界面显示
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void button_AddCustomer_Click(object sender, EventArgs e)
        {
            try
            {
                Setting.CustomerList.Add(new Customer { Name = textBox_Customer.Text.Trim() });
                string json = JsonConvert.SerializeObject(Setting, Formatting.Indented);
                File.WriteAllText(AppDomain.CurrentDomain.BaseDirectory + @"Setting.json", json);
                ShowInfo();
                textBox_Customer.Text = @"";
                SoundHelper.添加客户成功.Play();
                MessageBox.Show(@"添加成功!");
            }
            catch (Exception ex)
            {
                SoundHelper.添加客户失败.Play();
                MessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 删除客户按钮点击事件
        /// 从配置文件中删除选中的客户信息并更新界面显示
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void button_DelCustomer_Click(object sender, EventArgs e)
        {
            if (listView_Customer.SelectedItems.Count.Equals(0))
            {
                MessageBox.Show(@"请先选择要删除的客户!");
                return;
            }

            try
            {
                Setting.CustomerList.Remove(Setting.CustomerList.Find(a => a.Name == listView_Customer.SelectedItems[0].Text));
                string json = JsonConvert.SerializeObject(Setting, Formatting.Indented);
                File.WriteAllText(AppDomain.CurrentDomain.BaseDirectory + @"Setting.json", json);
                ShowInfo();
                SoundHelper.删除客户成功.Play();
                MessageBox.Show(@"删除客户成功!");
            }
            catch (Exception ex)
            {
                SoundHelper.删除客户失败.Play();
                MessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 添加型号按钮点击事件
        /// 将新产品型号信息添加到配置文件并更新界面显示
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void button_AddModel_Click(object sender, EventArgs e)
        {
            try
            {
                Setting.ModelList.Add(new Model { Name = textBox_Model.Text.Trim() });
                string json = JsonConvert.SerializeObject(Setting, Formatting.Indented);
                File.WriteAllText(AppDomain.CurrentDomain.BaseDirectory + @"Setting.json", json);
                ShowInfo();
                textBox_Model.Text = @"";
                SoundHelper.添加型号成功.Play();
                MessageBox.Show(@"添加型号成功!");
            }
            catch (Exception ex)
            {
                SoundHelper.添加型号失败.Play();
                MessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 删除型号按钮点击事件
        /// 从配置文件中删除选中的产品型号信息并更新界面显示
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void button_DelModel_Click(object sender, EventArgs e)
        {
            if (listView_Model.SelectedItems.Count.Equals(0))
            {
                MessageBox.Show(@"请先选择要删除的型号!");
                return;
            }

            try
            {
                Setting.ModelList.Remove(Setting.ModelList.Find(a => a.Name == listView_Model.SelectedItems[0].Text));
                string json = JsonConvert.SerializeObject(Setting, Formatting.Indented);
                File.WriteAllText(AppDomain.CurrentDomain.BaseDirectory + @"Setting.json", json);
                ShowInfo();
                SoundHelper.删除型号成功.Play();
                MessageBox.Show(@"删除成功!");
            }
            catch (Exception ex)
            {
                SoundHelper.删除型号失败.Play();
                Console.WriteLine(ex.Message);
            }
        }

        /// <summary>
        /// 导出Excel按钮点击事件
        /// 根据当前选中的标签页导出对应的产品数据到Excel文件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void toolStripSplitButton1_ButtonClick(object sender, EventArgs e)
        {
            if (tabControl1.SelectedIndex.Equals(1))
            {
                // 导出录入页面的产品列表
                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = @"Excel文件|*.xlsx";
                saveFileDialog.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                saveFileDialog.RestoreDirectory = true;
                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string filePath = saveFileDialog.FileName;
                    MiniExcel.SaveAs($@"{filePath}", ProductList, overwriteFile: true);
                    MessageBox.Show(@"保存成功!");
                }
            }
            else if (tabControl1.SelectedIndex.Equals(2))
            {
                // 导出追溯页面的查询结果
                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = @"Excel文件|*.xlsx";
                saveFileDialog.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                saveFileDialog.RestoreDirectory = true;
                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string filePath = saveFileDialog.FileName;
                    MiniExcel.SaveAs($@"{filePath}", ObjProductList, overwriteFile: true);
                    MessageBox.Show(@"保存成功!");
                }
            }
            else
            {
                MessageBox.Show(@"当前页面没有可导出内容!");
            }
        }

        /// <summary>
        /// 无线扫码器文本框按键事件
        /// 处理无线扫码器输入的箱号数据，当按下回车键时触发查询操作
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">按键事件参数</param>
        private void textBox_BleScan_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                Invoke(new Action(() =>
                {
                    try
                    {
                        string recData = textBox_BleScan.Text.Trim();
                        textBox_BleScan.Text = @"";

                        if (!string.IsNullOrWhiteSpace(BoxSn))
                        {
                            MessageBox.Show(@"请等待上一个查询完成!");
                            return;
                        }

                        BoxSn = recData;
                        label_BoxSn.Text = @"箱号:" + BoxSn + @" 查询中...";

                        // 从数据库中查询是否有该SN
                        ShipmentRecordProduct obj = DbHelper.FSql.Select<ShipmentRecordProduct>().Where(a => a.BoxSn == recData).First();
                        if (obj != null)
                        {
                            label_BoxSn.Text = @"箱号:" + BoxSn + @" 已存在!";
                            BoxSn = @"";
                            SoundHelper.录入失败.Play();
                            MessageBox.Show($@"该箱号已于{obj.AddTime:yyyy-MM-dd HH:mm:ss} 录入,请勿重复录入!");
                            return;
                        }

                        Task.Run(DoWork);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(@"扫描数据异常，" + ex.Message);
                    }
                }));
            }
        }
    }
}