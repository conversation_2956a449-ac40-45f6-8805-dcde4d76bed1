using System;

namespace ShipmentRecord
{
    /// <summary>
    /// 出货记录产品类
    /// 用于存储和管理产品出货的相关信息
    /// </summary>
    public class ShipmentRecordProduct
    {
        /// <summary>
        /// 产品序列号
        /// </summary>
        public string Sn { get; set; }

        /// <summary>
        /// 箱号 - 产品所在包装箱的编号
        /// </summary>
        public string BoxSn { get; set; }

        /// <summary>
        /// 产品型号
        /// </summary>
        public string Model { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// 添加时间 - 记录创建的时间戳
        /// 默认为当前系统时间
        /// </summary>
        public DateTime AddTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否删除 - 记录是否被删除
        /// </summary>
        public bool IsDelete { get; set; }
    }
}