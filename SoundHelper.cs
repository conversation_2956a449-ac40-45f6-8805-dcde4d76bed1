﻿using System;
using System.Media;

namespace ShipmentRecord
{
    /// <summary>
    ///     SoundHelper, 音效播放器
    /// </summary>
    public static class SoundHelper
    {
        #region InitSoundService,载入音效文件

        /// <summary>
        ///     InitSoundService,载入音效文件
        /// </summary>
        public static void InitSoundService()
        {
            登录成功.Load();
            录入成功.Load();
            录入失败.Load();
            查询成功.Load();
            查询失败.Load();
            删除成功.Load();
            删除失败.Load();
            添加客户成功.Load();
            添加客户失败.Load();
            删除客户成功.Load();
            删除客户失败.Load();
            添加型号成功.Load();
            添加型号失败.Load();
            删除型号成功.Load();
            删除型号失败.Load();
        }

        #endregion

        #region 设置音效文件路径

        public static SoundPlayer 登录成功 { get; } = new SoundPlayer { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/登录成功.wav" };
        public static SoundPlayer 录入成功 { get; } = new SoundPlayer { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/录入成功.wav" };
        public static SoundPlayer 录入失败 { get; } = new SoundPlayer { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/录入失败.wav" };
        public static SoundPlayer 查询成功 { get; } = new SoundPlayer { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/查询成功.wav" };
        public static SoundPlayer 查询失败 { get; } = new SoundPlayer { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/查询失败.wav" };
        public static SoundPlayer 删除成功 { get; } = new SoundPlayer { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/删除成功.wav" };
        public static SoundPlayer 删除失败 { get; } = new SoundPlayer { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/删除失败.wav" };
        public static SoundPlayer 添加客户成功 { get; } = new SoundPlayer { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/添加客户成功.wav" };
        public static SoundPlayer 添加客户失败 { get; } = new SoundPlayer { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/添加客户失败.wav" };
        public static SoundPlayer 删除客户成功 { get; } = new SoundPlayer { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/删除客户成功.wav" };
        public static SoundPlayer 删除客户失败 { get; } = new SoundPlayer { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/删除客户失败.wav" };
        public static SoundPlayer 添加型号成功 { get; } = new SoundPlayer { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/添加型号成功.wav" };
        public static SoundPlayer 添加型号失败 { get; } = new SoundPlayer { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/添加型号失败.wav" };
        public static SoundPlayer 删除型号成功 { get; } = new SoundPlayer { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/删除型号成功.wav" };
        public static SoundPlayer 删除型号失败 { get; } = new SoundPlayer { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/删除型号失败.wav" };

        #endregion
    }
}