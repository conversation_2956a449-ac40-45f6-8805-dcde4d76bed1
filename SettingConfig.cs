using System.Collections.Generic;
using System.Reflection;

namespace ShipmentRecord
{
    /// <summary>
    /// 设置配置类
    /// 用于存储和管理应用程序的全局配置信息
    /// </summary>
    public class SettingConfig
    {
        /// <summary>
        /// 项目名称
        /// </summary>
        public string Project { get; set; }
        
        /// <summary>
        /// 版本号
        /// </summary>
        public string Version { get; set; }
        
        /// <summary>
        /// 更新时间
        /// </summary>
        public string UpdateTime { get; set; }
        
        /// <summary>
        /// 客户列表
        /// 存储系统中所有客户信息的集合
        /// </summary>
        public List<Customer> CustomerList { get; set; } = new List<Customer>();
        
        /// <summary>
        /// 型号列表
        /// 存储系统中所有产品型号信息的集合
        /// </summary>
        public List<Model> ModelList { get; set; } = new List<Model>();
    }
}